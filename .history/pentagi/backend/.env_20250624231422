# PentAGI Environment Variables

CORS_ORIGINS=http://localhost:*,https://localhost:*

COOKIE_SIGNING_SALT=pentagi_test_salt_2025

# Allow to interact with user while executing tasks
ASK_USER=true

## LLM Providers - Using OpenAI for testing
OPEN_AI_KEY=********************************************************************************************************************************************************************
OPEN_AI_SERVER_URL=https://api.openai.com/v1

ANTHROPIC_API_KEY=************************************************************************************************************
ANTHROPIC_SERVER_URL=https://api.anthropic.com/v1

## Custom LLM provider
LLM_SERVER_URL=
LLM_SERVER_KEY=
LLM_SERVER_MODEL=
LLM_SERVER_CONFIG_PATH=
LLM_SERVER_LEGACY_REASONING=false

## Embedding
EMBEDDING_URL=
EMBEDDING_KEY=
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_PROVIDER=openai
EMBEDDING_BATCH_SIZE=100

## Summarizer
SUMMARIZER_PRESERVE_LAST=true
SUMMARIZER_USE_QA=true
SUMMARIZER_SUM_MSG_HUMAN_IN_QA=false
SUMMARIZER_LAST_SEC_BYTES=51200
SUMMARIZER_MAX_BP_BYTES=16384
SUMMARIZER_MAX_QA_SECTIONS=10
SUMMARIZER_MAX_QA_BYTES=65536
SUMMARIZER_KEEP_QA_SECTIONS=1

## Assistant
ASSISTANT_USE_AGENTS=true
ASSISTANT_SUMMARIZER_PRESERVE_LAST=true
ASSISTANT_SUMMARIZER_LAST_SEC_BYTES=76800
ASSISTANT_SUMMARIZER_MAX_BP_BYTES=16384
ASSISTANT_SUMMARIZER_MAX_QA_SECTIONS=7
ASSISTANT_SUMMARIZER_MAX_QA_BYTES=76800
ASSISTANT_SUMMARIZER_KEEP_QA_SECTIONS=3

## HTTP proxy to use it in isolation environment
PROXY_URL=

## Scraper URLs and settings
SCRAPER_PUBLIC_URL=
SCRAPER_PRIVATE_URL=*********************************/
LOCAL_SCRAPER_USERNAME=testuser
LOCAL_SCRAPER_PASSWORD=testpass
LOCAL_SCRAPER_MAX_CONCURRENT_SESSIONS=10

## Web server settings
PUBLIC_URL=https://localhost:8443
STATIC_DIR=
STATIC_URL=
SERVER_PORT=8443
SERVER_HOST=0.0.0.0
SERVER_SSL_CRT=
SERVER_SSL_KEY=
SERVER_USE_SSL=true

## OAuth google
OAUTH_GOOGLE_CLIENT_ID=
OAUTH_GOOGLE_CLIENT_SECRET=

## OAuth github
OAUTH_GITHUB_CLIENT_ID=
OAUTH_GITHUB_CLIENT_SECRET=

## Google search engine API
GOOGLE_API_KEY=
GOOGLE_CX_KEY=

## Traversaal search engine API
TRAVERSAAL_API_KEY=

## Tavily search engine API
TAVILY_API_KEY=

## Perplexity search engine API
PERPLEXITY_API_KEY=
PERPLEXITY_MODEL=sonar-pro
PERPLEXITY_CONTEXT_SIZE=medium

## Langfuse observability settings
LANGFUSE_BASE_URL=
LANGFUSE_PROJECT_ID=
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=

## OpenTelemetry observability settings
OTEL_HOST=

## Docker client settings to run primary terminal container
DOCKER_HOST=unix:///var/run/docker.sock
DOCKER_TLS_VERIFY=
DOCKER_CERT_PATH=

## Docker settings inside primary terminal container
DOCKER_INSIDE=true
DOCKER_NET_ADMIN=true
DOCKER_SOCKET=/var/run/docker.sock
DOCKER_NETWORK=pentagi-network
DOCKER_PUBLIC_IP=0.0.0.0
DOCKER_WORK_DIR=
DOCKER_DEFAULT_IMAGE=

# Postgres (pgvector) settings
PENTAGI_POSTGRES_USER=postgres
PENTAGI_POSTGRES_PASSWORD=pentagi_test_password_2025
PENTAGI_POSTGRES_DB=pentagidb
# 本地开发时使用的数据库连接字符串，将pgvector替换为localhost
DATABASE_URL=postgres://postgres:pentagi_test_password_2025@localhost:5432/pentagidb?sslmode=disable


# Langfuse Environment Variables

## Langfuse Postgres
LANGFUSE_POSTGRES_USER=postgres
LANGFUSE_POSTGRES_PASSWORD=langfuse_test_password_2025
LANGFUSE_POSTGRES_DB=langfuse
LANGFUSE_POSTGRES_VERSION=16

## Langfuse Clickhouse
LANGFUSE_CLICKHOUSE_USER=clickhouse
LANGFUSE_CLICKHOUSE_PASSWORD=clickhouse_test_password_2025
LANGFUSE_CLICKHOUSE_URL=http://langfuse-clickhouse:8123
LANGFUSE_CLICKHOUSE_MIGRATION_URL=clickhouse://langfuse-clickhouse:9000
LANGFUSE_CLICKHOUSE_CLUSTER_ENABLED=false

## Langfuse S3
LANGFUSE_S3_BUCKET=langfuse
LANGFUSE_S3_REGION=auto
LANGFUSE_S3_ACCESS_KEY_ID=test_access_key_2025
LANGFUSE_S3_SECRET_ACCESS_KEY=test_secret_key_2025
LANGFUSE_S3_ENDPOINT=http://langfuse-minio:9000
LANGFUSE_S3_FORCE_PATH_STYLE=true
LANGFUSE_S3_EVENT_UPLOAD_PREFIX=events/
LANGFUSE_S3_MEDIA_UPLOAD_PREFIX=media/

## Langfuse Redis
LANGFUSE_REDIS_HOST=langfuse-redis
LANGFUSE_REDIS_PORT=6379
LANGFUSE_REDIS_AUTH=redis_test_password_2025

## Langfuse web app security settings
LANGFUSE_SALT=langfuse_test_salt_2025
LANGFUSE_ENCRYPTION_KEY=1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

## Langfuse web app nextauth settings
LANGFUSE_NEXTAUTH_URL=http://localhost:4000
LANGFUSE_NEXTAUTH_SECRET=nextauth_test_secret_2025

## Langfuse extra settings
LANGFUSE_ENABLE_EXPERIMENTAL_FEATURES=true
LANGFUSE_TELEMETRY_ENABLED=false
LANGFUSE_LOG_LEVEL=info

## Langfuse init settings
LANGFUSE_INIT_ORG_ID=test_org_id_2025
LANGFUSE_INIT_ORG_NAME=PentAGI Test Org
LANGFUSE_INIT_PROJECT_ID=test_project_id_2025
LANGFUSE_INIT_PROJECT_NAME=PentAGI Test
LANGFUSE_INIT_PROJECT_PUBLIC_KEY=pk-lf-test-2025-0000-0000-0000-000000000000
LANGFUSE_INIT_PROJECT_SECRET_KEY=sk-lf-test-2025-0000-0000-0000-000000000000
LANGFUSE_INIT_USER_EMAIL=<EMAIL>
LANGFUSE_INIT_USER_NAME=admin
LANGFUSE_INIT_USER_PASSWORD=admin_test_password_2025

## Langfuse SDK sync settings
LANGFUSE_SDK_CI_SYNC_PROCESSING_ENABLED=false
LANGFUSE_READ_FROM_POSTGRES_ONLY=false
LANGFUSE_READ_FROM_CLICKHOUSE_ONLY=true
LANGFUSE_RETURN_FROM_CLICKHOUSE=true

## Langfuse license settings
LANGFUSE_EE_LICENSE_KEY=

## Langfuse OpenTelemetry settings
LANGFUSE_OTEL_EXPORTER_OTLP_ENDPOINT=
LANGFUSE_OTEL_SERVICE_NAME=

## Langfuse custom oauth2 settings
LANGFUSE_AUTH_CUSTOM_CLIENT_ID=
LANGFUSE_AUTH_CUSTOM_CLIENT_SECRET=
LANGFUSE_AUTH_CUSTOM_ISSUER=
LANGFUSE_AUTH_CUSTOM_NAME=PentAGI
LANGFUSE_AUTH_CUSTOM_SCOPE=openid email profile
LANGFUSE_AUTH_CUSTOM_CLIENT_AUTH_METHOD=client_secret_post
LANGFUSE_AUTH_CUSTOM_ALLOW_ACCOUNT_LINKING=true

## Langfuse auth settings
LANGFUSE_AUTH_DISABLE_SIGNUP=false
LANGFUSE_AUTH_SESSION_MAX_AGE=240

## Langfuse allowed organization creators
LANGFUSE_ALLOWED_ORGANIZATION_CREATORS=<EMAIL>

## Langfuse default settings for new users
LANGFUSE_DEFAULT_ORG_ID=test_org_id_2025
LANGFUSE_DEFAULT_PROJECT_ID=test_project_id_2025
LANGFUSE_DEFAULT_ORG_ROLE=VIEWER
LANGFUSE_DEFAULT_PROJECT_ROLE=VIEWER
