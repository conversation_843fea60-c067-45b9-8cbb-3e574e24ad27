package main

import (
	"context"
	"fmt"
	"log"
	"pentagi/pkg/config"

	"github.com/vxcontrol/langchaingo/llms"
	"github.com/vxcontrol/langchaingo/llms/anthropic"
)

func main() {
	// 加载配置
	cfg, err := config.NewConfig()
	if err != nil {
		log.Fatalf("无法加载配置: %v", err)
	}

	fmt.Printf("API Key: %s...\n", cfg.AnthropicAPIKey[:20])
	fmt.Printf("Base URL: %s\n", cfg.AnthropicServerURL)

	// 创建Anthropic客户端
	client, err := anthropic.New(
		anthropic.WithToken(cfg.AnthropicAPIKey),
		anthropic.WithModel("claude-3-7-sonnet-20250219"),
		anthropic.WithBaseURL(cfg.AnthropicServerURL),
	)
	if err != nil {
		log.Fatalf("无法创建Anthropic客户端: %v", err)
	}

	// 测试API调用
	ctx := context.Background()
	response, err := client.GenerateContent(ctx, []llms.MessageContent{
		llms.TextParts(llms.ChatMessageTypeHuman, "Hello, just say 'API test successful'"),
	}, llms.WithMaxTokens(100))

	if err != nil {
		log.Fatalf("❌ API测试失败: %v", err)
	}

	fmt.Println("✅ API测试成功!")
	fmt.Printf("响应: %s\n", response.Choices[0].Content)
}
