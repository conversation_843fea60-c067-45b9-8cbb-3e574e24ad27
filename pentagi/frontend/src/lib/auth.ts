export function isAuthenticated() {
    try {
        const auth = localStorage.getItem('auth');

        if (!auth) {
            return false;
        }

        const data = JSON.parse(auth);
        
        // 检查后端返回的 auth 数据结构
        // 如果有 expires_at 直接使用
        if (data.expires_at) {
            const now = new Date().toISOString();
            return data.expires_at > now;
        }
        
        // 如果有 exp 字段（Unix 时间戳格式）
        if (data.exp) {
            const now = Math.floor(Date.now() / 1000);
            return data.exp > now;
        }
        
        // 如果存在 user 字段，则认为用户已登录
        // 这个简化的检查假设只要存储了用户信息就是已认证的
        if (data.user || (data.type === 'user' && data.data)) {
            return true;
        }

        return false;
    } catch (error) {
        console.error('Error checking authentication status:', error);
        return false;
    }
}
