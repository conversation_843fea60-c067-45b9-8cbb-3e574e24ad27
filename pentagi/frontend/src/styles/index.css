@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        /* 国家电网浅色主题配色 */
        --background: 0 0% 100%;
        --foreground: 210 11% 15%; /* 深灰文本 */
        --card: 0 0% 100%;
        --card-foreground: 210 11% 15%;
        --popover: 0 0% 100%;
        --popover-foreground: 210 11% 15%;
        --primary: 142 76% 36%; /* 国家电网绿色 #00A651 */
        --primary-foreground: 0 0% 100%;
        --secondary: 142 100% 95%; /* 浅绿色背景 */
        --secondary-foreground: 142 76% 25%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 213 94% 68%; /* 科技蓝色 */
        --accent-foreground: 0 0% 100%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border: 214.3 31.8% 91.4%;
        --input: 214.3 31.8% 91.4%;
        --ring: 142 76% 36%; /* 绿色焦点环 */
        --radius: 0.5rem;
        --chart-1: 142 76% 36%; /* 绿色 */
        --chart-2: 213 94% 68%; /* 蓝色 */
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        /* 国家电网深色主题配色 */
        --background: 210 11% 15%; /* 深灰背景 */
        --foreground: 0 0% 98%;
        --card: 210 11% 18%; /* 稍浅的卡片背景 */
        --card-foreground: 0 0% 98%;
        --popover: 210 11% 18%;
        --popover-foreground: 0 0% 98%;
        --primary: 142 76% 45%; /* 稍亮的绿色用于深色主题 */
        --primary-foreground: 0 0% 100%;
        --secondary: 210 11% 25%; /* 深灰次要色 */
        --secondary-foreground: 0 0% 98%;
        --muted: 210 11% 25%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 213 94% 75%; /* 稍亮的蓝色用于深色主题 */
        --accent-foreground: 210 11% 15%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 210 11% 25%;
        --input: 210 11% 25%;
        --ring: 142 76% 45%; /* 绿色焦点环 */
        --chart-1: 142 76% 45%; /* 绿色 */
        --chart-2: 213 94% 75%; /* 蓝色 */
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 210 11% 12%; /* 更深的侧边栏背景 */
        --sidebar-foreground: 0 0% 95%;
        --sidebar-primary: 142 76% 45%; /* 绿色主色 */
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 210 11% 20%;
        --sidebar-accent-foreground: 0 0% 95%;
        --sidebar-border: 210 11% 20%;
        --sidebar-ring: 142 76% 45%;
    }

    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: transparent;
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
        background: hsl(var(--primary) / 0.4);
        border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--primary) / 0.6);
    }

    * {
        scrollbar-color: hsl(var(--primary) / 0.4) transparent;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

@layer components {
    .btn-state-grid {
        @apply bg-state-grid-gradient text-white shadow-state-grid
        hover:shadow-state-grid-hover transition-shadow rounded-md px-4 py-2;
    }
    
    .card-state-grid {
        @apply bg-white border border-primary/20 shadow-state-grid rounded-lg;
    }
    
    .input-state-grid {
        @apply border-primary/30 focus:border-primary focus:ring-1 focus:ring-primary/50
        rounded-md transition-all;
    }
    
    .tab-state-grid {
        @apply text-foreground hover:text-primary border-b-2 border-transparent
        hover:border-primary/40 transition-all py-2;
    }
    
    .tab-state-grid-active {
        @apply text-primary border-b-2 border-primary font-medium py-2;
    }
}

.prose-xs {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.prose-xs hr {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs h1,
.prose-xs h2,
.prose-xs h3,
.prose-xs h4,
.prose-xs h5,
.prose-xs h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

.prose-xs p {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs p:first-child {
    margin-top: 0;
}

.prose-xs p:last-child {
    margin-bottom: 0;
}

.prose-xs pre {
    margin-top: 1.5em;
    font-size: 1em;
    line-height: 1.5;
}

.prose-xs ul,
.prose-xs ol {
    margin-top: 1.5em;
    margin-bottom: 1.5em;
}

.prose-xs ul ul,
.prose-xs ul ol,
.prose-xs ol ol,
.prose-xs ol ul {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs li {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs ul > li > p,
.prose-xs ol > li > p,
.prose-xs ul > li > pre,
.prose-xs ol > li > pre {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
}

.prose-xs ul > li > p:first-child,
.prose-xs ol > li > p:first-child {
    margin-top: 0;
}

.prose-xs ul > li > p:last-child,
.prose-xs ol > li > p:last-child {
    margin-bottom: 0;
}

.prose-xs h1 + ul,
.prose-xs h1 + ol,
.prose-xs h2 + ul,
.prose-xs h2 + ol,
.prose-xs h3 + ul,
.prose-xs h3 + ol,
.prose-xs h4 + ul,
.prose-xs h4 + ol,
.prose-xs h5 + ul,
.prose-xs h5 + ol,
.prose-xs h6 + ul,
.prose-xs h6 ol {
    margin-top: 0;
}

.prose-xs :first-child {
    margin-top: 0;
}

.prose-xs :last-child {
    margin-bottom: 0;
}

.prose-fixed h1,
.prose-fixed h2,
.prose-fixed h3,
.prose-fixed h4,
.prose-fixed h5,
.prose-fixed h6 {
    font-size: 1em;
    line-height: 1.5;
}

.prose pre code.hljs {
    color: inherit;
    background: inherit;
}

.prose pre code.hljs {
    padding: 0;
    overflow: visible;
}
